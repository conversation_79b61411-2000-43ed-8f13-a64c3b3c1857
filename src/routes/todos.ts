import { Hono } from 'hono'
import { TodoService } from '@/services/todoService'
import type { CreateTodoRequest, UpdateTodoRequest, CreateCategoryRequest } from '@/types/todos'
import { Session, User } from 'better-auth'

const todosRouter = new Hono<{
  Bindings: CloudflareBindings
  Variables: {
    user: User | null
    session: Session | null
  }
}>().basePath('/todos')

// 获取用户的所有待办事项
todosRouter.get('/', async (c) => {
  const todoService = new TodoService(c.env.DB)
  const user = c.get('user')

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  // 获取查询参数
  const completed = c.req.query('completed')
  const categoryId = c.req.query('categoryId')
  const priority = c.req.query('priority') as any
  const parentId = c.req.query('parentId')

  const filters: any = {}
  if (completed !== undefined) filters.completed = completed === 'true'
  if (categoryId) filters.categoryId = categoryId
  if (priority) filters.priority = priority
  if (parentId !== undefined) filters.parentId = parentId === 'null' ? null : parentId

  const userTodos = await todoService.getTodosByUser(user.id, filters)

  return c.json({ todos: userTodos })
})

// 创建新的待办事项
todosRouter.post('/', async (c) => {
  const todoService = new TodoService(c.env.DB)
  const user = c.get('user')

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const body: CreateTodoRequest = await c.req.json()

  if (!body.title) {
    return c.json({ error: 'Title is required' }, 400)
  }

  const newTodo = await todoService.createTodo(user.id, body)

  return c.json({ todo: newTodo }, 201)
})

// 获取单个待办事项详情
todosRouter.get('/:id', async (c) => {
  const todoService = new TodoService(c.env.DB)
  const user = c.get('user')
  const todoId = c.req.param('id')

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const todo = await todoService.getTodoById(todoId, user.id)

  if (!todo) {
    return c.json({ error: 'Todo not found' }, 404)
  }

  return c.json({ todo })
})

// 更新待办事项
todosRouter.put('/:id', async (c) => {
  const db = drizzle(c.env.DB)
  const user = c.get('user')
  const todoId = c.req.param('id')
  
  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const body = await c.req.json()
  const { title, description, completed, priority, dueDate, categoryId } = body

  const updateData: any = {
    updatedAt: new Date(),
  }

  if (title !== undefined) updateData.title = title
  if (description !== undefined) updateData.description = description
  if (completed !== undefined) {
    updateData.completed = completed
    updateData.completedAt = completed ? new Date() : null
  }
  if (priority !== undefined) updateData.priority = priority
  if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null
  if (categoryId !== undefined) updateData.categoryId = categoryId

  const updatedTodo = await db
    .update(todos)
    .set(updateData)
    .where(and(eq(todos.id, todoId), eq(todos.userId, user.id)))
    .returning()

  if (updatedTodo.length === 0) {
    return c.json({ error: 'Todo not found' }, 404)
  }

  return c.json({ todo: updatedTodo[0] })
})

// 删除待办事项
todosRouter.delete('/:id', async (c) => {
  const db = drizzle(c.env.DB)
  const user = c.get('user')
  const todoId = c.req.param('id')
  
  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const deletedTodo = await db
    .delete(todos)
    .where(and(eq(todos.id, todoId), eq(todos.userId, user.id)))
    .returning()

  if (deletedTodo.length === 0) {
    return c.json({ error: 'Todo not found' }, 404)
  }

  return c.json({ message: 'Todo deleted successfully' })
})

// 获取用户的分类
todosRouter.get('/categories', async (c) => {
  const db = drizzle(c.env.DB)
  const user = c.get('user')
  
  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const userCategories = await db
    .select()
    .from(categories)
    .where(eq(categories.userId, user.id))
    .orderBy(asc(categories.name))

  return c.json({ categories: userCategories })
})

// 创建新分类
todosRouter.post('/categories', async (c) => {
  const db = drizzle(c.env.DB)
  const user = c.get('user')
  
  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const body = await c.req.json()
  const { name, color = '#3B82F6', icon } = body

  if (!name) {
    return c.json({ error: 'Name is required' }, 400)
  }

  const newCategory = await db
    .insert(categories)
    .values({
      name,
      color,
      icon,
      userId: user.id,
    })
    .returning()

  return c.json({ category: newCategory[0] }, 201)
})

// 获取用户的统计信息
todosRouter.get('/stats', async (c) => {
  const todoService = new TodoService(c.env.DB)
  const user = c.get('user')

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const stats = await todoService.getTodoStats(user.id)

  return c.json({ stats })
})

// 获取子任务
todosRouter.get('/:id/subtasks', async (c) => {
  const todoService = new TodoService(c.env.DB)
  const user = c.get('user')
  const parentId = c.req.param('id')

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const subtasks = await todoService.getSubtasks(parentId, user.id)

  return c.json({ subtasks })
})

export default todosRouter
