import { Hono } from 'hono'
import { auth } from '@/lib/better-auth'
import type { HonoVariables } from '@/types/global'

const app = new Hono<{
  Bindings: CloudflareBindings
  Variables: HonoVariables
}>()

// better-auth 处理器
app.on(['POST', 'GET'], '/auth/*', async (c) => {
  const response = await auth(c.env).handler(c.req.raw)

  // 确保响应包含正确的 CORS 头部
  response.headers.set('Access-Control-Allow-Credentials', 'true')
  return response
})

export default app
