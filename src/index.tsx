import { Hono } from 'hono'
import { authMiddleware<PERSON><PERSON>ler, corsMiddlewareHandler } from '@/middleware'
import { renderer } from '@/renderer'
import customers from '@/routes/customer'
import posts from '@/routes/posts'
import todosRouter from '@/routes/todos'
import type { HonoVariables } from '@/types/global'

const app = new Hono<{
  Bindings: CloudflareBindings
  Variables: HonoVariables
}>()

// 中间件设置
app.use(renderer)
app.use('*', corsMiddlewareHandler)
app.use('*', authMiddlewareHandler)

app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
app.route('/api', posts)
app.route('/api', customers)
app.route('/api', todosRouter)
app.route('/api', authRoutes)

export default app
